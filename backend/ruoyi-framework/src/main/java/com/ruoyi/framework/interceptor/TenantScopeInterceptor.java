package com.ruoyi.framework.interceptor;

import com.ruoyi.common.context.TenantContext;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysTenantIgnoreTableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.*;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.util.*;

/**
 * 多租户SQL拦截器
 */
@Component
@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})})
public class TenantScopeInterceptor implements Interceptor {

    private static final String TENANT_COLUMN = "shop_id";

    // 默认忽略表列表，用于Service未初始化时的回退
    private static final String[] DEFAULT_IGNORE_TABLES = {"wx_login","creator_info","sys_tenant", "gen_table", "gen_table_column", "information_schema.tables", "information_schema.columns", "wendao_shop_info", "wendao_sms_log", "wendao_sys_login_log", "wendao_sys_user", "wendao_wx_qrcode_record", "sys_trial_permission", "sys_tenant_ignore_tables"};

    @Autowired
    private ApplicationContext applicationContext;

    // 延迟初始化的Service，避免循环依赖
    private volatile ISysTenantIgnoreTableService tenantIgnoreTableService;

    /**
     * 延迟获取租户忽略表服务，避免循环依赖
     */
    private ISysTenantIgnoreTableService getTenantIgnoreTableService() {
        if (tenantIgnoreTableService == null) {
            synchronized (this) {
                if (tenantIgnoreTableService == null) {
                    try {
                        tenantIgnoreTableService = applicationContext.getBean(ISysTenantIgnoreTableService.class);
                    } catch (Exception e) {
                        System.out.println("tenantIgnoreTableService is null");
                        // 如果获取失败（可能还在初始化阶段），返回null，使用默认行为
                        return null;
                    }
                }
            }
        }
        return tenantIgnoreTableService;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);

        //MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");

        BoundSql boundSql = statementHandler.getBoundSql();
        String sql = boundSql.getSql();
        //System.out.println("sql:"+sql);

        // 判断是否需要进行多租户过滤
        if (needProcess(sql)) {
            String shopId = TenantContext.getShopId();
            // 如果租户ID为null，直接返回原始SQL
            if (shopId == null) {
                return invocation.proceed();
            }

            // 解析SQL
            Statement statement = CCJSqlParserUtil.parse(sql);

            if (statement instanceof Select) {
                // 处理SELECT语句
                handleSelect((Select) statement, shopId);
            } else if (statement instanceof Insert) {
                // 处理INSERT语句
                handleInsert((Insert) statement, shopId);
            } else if (statement instanceof Update) {
                // 处理UPDATE语句
                handleUpdate((Update) statement, shopId);
            } else if (statement instanceof Delete) {
                // 处理DELETE语句
                handleDelete((Delete) statement, shopId);
            }

            // 重写SQL
            metaObject.setValue("delegate.boundSql.sql", statement.toString());
        }

        return invocation.proceed();
    }

    protected void handleSelect(Select select, String shopId) {
        // 获取SQL字符串
        String sql = select.toString();

        // 处理特定的SQL
        String targetSql = "SELECT d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.STATUS, d.del_flag, d.create_by, d.create_time FROM sys_dept d WHERE d.del_flag = '0' AND (d.dept_id IN (SELECT dept_id FROM sys_role_dept WHERE role_id = 2)) ORDER BY d.parent_id, d.order_num";

        // 去除所有多余的空格、换行和括号周围的空格
        String normalizedSql = sql.replaceAll("\\s+", " ")  // 替换多个空格为单个空格
                .replaceAll("\\s*\\(\\s*", "(")  // 移除左括号周围的空格
                .replaceAll("\\s*\\)\\s*", ")")  // 移除右括号周围的空格
                .trim();
        String normalizedTargetSql = targetSql.replaceAll("\\s+", " ")
                .replaceAll("\\s*\\(\\s*", "(")
                .replaceAll("\\s*\\)\\s*", ")")
                .trim();

        // 从SQL中提取role_id值
        String roleIdPattern = "role_id\\s*=\\s*\\d+";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(roleIdPattern);

        // 处理normalizedSql
        java.util.regex.Matcher sqlMatcher = pattern.matcher(normalizedSql);
        if (sqlMatcher.find()) {
            normalizedSql = normalizedSql.replaceAll(roleIdPattern, "role_id = X");
        }

        // 处理normalizedTargetSql
        java.util.regex.Matcher targetSqlMatcher = pattern.matcher(normalizedTargetSql);
        if (targetSqlMatcher.find()) {
            normalizedTargetSql = normalizedTargetSql.replaceAll(roleIdPattern, "role_id = X");
        }

        if (normalizedSql.equalsIgnoreCase(normalizedTargetSql)) {
            // 从原始SQL中提取实际的role_id值
            sqlMatcher = pattern.matcher(sql.replaceAll("\\s+", " "));
            String actualRoleId = "2"; // 默认值
            if (sqlMatcher.find()) {
                String roleIdMatch = sqlMatcher.group();
                actualRoleId = roleIdMatch.replaceAll(".*=\\s*", "").trim();
            }

            // 如果是目标SQL，直接替换为带租户ID的版本
            String modifiedSql = "SELECT d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.STATUS, d.del_flag, d.create_by, d.create_time FROM sys_dept d WHERE d.del_flag = '0' AND (d.dept_id IN (SELECT dept_id FROM sys_role_dept WHERE role_id = " + actualRoleId + " and shop_id='" + shopId + "')) AND d.shop_id = '" + shopId + "' ORDER BY d.parent_id, d.order_num";
            try {
                // 将修改后的SQL解析回Select对象
                select.setSelectBody(((Select) CCJSqlParserUtil.parse(modifiedSql)).getSelectBody());
                return;
            } catch (Exception e) {
                // 如果解析失败，继续使用原有处理方式
            }
        }

        // 处理WITH项
        if (select.getWithItemsList() != null) {
            for (WithItem withItem : select.getWithItemsList()) {
                if (withItem.getSubSelect() != null) {
                    processSelectBody(withItem.getSubSelect().getSelectBody(), shopId);
                }
            }
        }
        // 处理主查询
        SelectBody selectBody = select.getSelectBody();
        processSelectBody(selectBody, shopId);
    }

    protected void processSelectBody(SelectBody selectBody, String shopId) {
        // 如果shopId为空，不进行处理
        if (StringUtils.isBlank(shopId)) {
            return;
        }

        if (selectBody instanceof PlainSelect) {
            processPlainSelect((PlainSelect) selectBody, shopId);
        } else if (selectBody instanceof SetOperationList) {
            SetOperationList setOperationList = (SetOperationList) selectBody;
            if (setOperationList.getSelects() != null) {
                for (SelectBody select : setOperationList.getSelects()) {
                    processSelectBody(select, shopId);
                }
            }
        }
    }

    protected void processPlainSelect(PlainSelect plainSelect, String shopId) {
        // 如果shopId为空，不进行处理
        if (StringUtils.isBlank(shopId)) {
            return;
        }

        // 处理 SELECT 项中的子查询
        List<SelectItem> selectItems = plainSelect.getSelectItems();
        if (selectItems != null) {
            for (SelectItem item : selectItems) {
                if (item instanceof SelectExpressionItem) {
                    Expression expression = ((SelectExpressionItem) item).getExpression();
                    if (expression instanceof SubSelect) {
                        // 处理SELECT项中的子查询
                        SubSelect subSelect = (SubSelect) expression;
                        processSelectBody(subSelect.getSelectBody(), shopId);
                    } else if (expression instanceof CaseExpression) {
                        CaseExpression caseExpr = (CaseExpression) expression;
                        // 处理 WHEN 子句
                        if (caseExpr.getWhenClauses() != null) {
                            for (WhenClause whenClause : caseExpr.getWhenClauses()) {
                                processWhereSubSelect(whenClause.getWhenExpression(), shopId);
                            }
                        }
                        // 处理 ELSE 子句
                        if (caseExpr.getElseExpression() != null) {
                            processWhereSubSelect(caseExpr.getElseExpression(), shopId);
                        }
                    }
                }
            }
        }

        // 处理FROM
        FromItem fromItem = plainSelect.getFromItem();
        List<Join> joins = plainSelect.getJoins();
        Expression whereExpression = null;
        boolean mustAddTableName = false;//默认有别名
        Map<String, Boolean> joinTablesMustAddTableName = new HashMap<>();
        //先判断form表和join表,如果都有,且都没有别名
        if (fromItem != null && joins != null) {
            if (fromItem instanceof Table && !isIgnoreTable(((Table) fromItem).getName()) && !StringUtils.equalsIgnoreCase(((Table) fromItem).getName(), "temp_table")) {
                mustAddTableName = (((Table) fromItem).getAlias() == null);
            }
            for (Join join : joins) {
                FromItem rightItem = join.getRightItem();
                if (rightItem instanceof Table && !isIgnoreTable(((Table) rightItem).getName()) && !StringUtils.equalsIgnoreCase(((Table) rightItem).getName(), "temp_table")) {
                    joinTablesMustAddTableName.put(((Table) rightItem).getName(), ((Table) rightItem).getAlias() == null);
                }
            }
        }

        if (fromItem != null) {
            processFromItem(fromItem, shopId);
            // 如果是表且不在忽略列表中，添加租户条件
            if (fromItem instanceof Table && !isIgnoreTable(((Table) fromItem).getName())) {
                // 检查是否是CTE（公共表表达式）中的引用
                String tableName = ((Table) fromItem).getName();
                if (!tableName.equalsIgnoreCase("temp_table")) {  // 如果不是临时表的引用，才添加租户条件
                    EqualsTo equalsTo = new EqualsTo();
                    // 只在有别名时才添加表名前缀
                    String columnName = ((Table) fromItem).getAlias() != null ?
                            ((Table) fromItem).getAlias().getName() + "." + TENANT_COLUMN : mustAddTableName ? tableName + "." + TENANT_COLUMN : TENANT_COLUMN;
                    equalsTo.setLeftExpression(new Column(columnName));
                    equalsTo.setRightExpression(new StringValue(shopId));
                    whereExpression = equalsTo;
                }
            }
        }

        // 处理JOIN和逗号连接
        if (joins != null) {
            for (Join join : joins) {
                FromItem rightItem = join.getRightItem();
                if (rightItem instanceof Table && !isIgnoreTable(((Table) rightItem).getName())) {
                    processFromItem(rightItem, shopId);

                    // 检查是否是CTE中的引用
                    String tableName = ((Table) rightItem).getName();
                    if (!tableName.equalsIgnoreCase("temp_table")) {  // 如果不是临时表的引用，才添加租户条件
                        // 创建租户条件
                        EqualsTo equalsTo = new EqualsTo();
                        String columnName = ((Table) rightItem).getAlias() != null ?
                                ((Table) rightItem).getAlias().getName() + "." + TENANT_COLUMN : joinTablesMustAddTableName.get(tableName) != null && joinTablesMustAddTableName.get(tableName) ? tableName + "." + TENANT_COLUMN : TENANT_COLUMN;
                        equalsTo.setLeftExpression(new Column(columnName));
                        equalsTo.setRightExpression(new StringValue(shopId));

                        // 将新的租户条件与现有的whereExpression组合
                        whereExpression = whereExpression == null ? equalsTo : new AndExpression(whereExpression, equalsTo);
                    }
                }
            }
        }

        // 处理WHERE
        Expression where = plainSelect.getWhere();
        if (where != null) {
            // 先处理WHERE中的子查询
            processWhereSubSelect(where, shopId);
            // 将租户条件与现有WHERE条件组合
            if (whereExpression != null) {
                whereExpression = new AndExpression(where, whereExpression);
            }
        }

        // 设置最终的WHERE条件
        if (whereExpression != null) {
            plainSelect.setWhere(whereExpression);
        }
    }

    protected void processFromItem(FromItem fromItem, String shopId) {
        // 如果shopId为空，不进行处理
        if (StringUtils.isBlank(shopId)) {
            return;
        }

        if (fromItem instanceof SubSelect) {
            SubSelect subSelect = (SubSelect) fromItem;
            if (subSelect.getSelectBody() != null) {
                processSelectBody(subSelect.getSelectBody(), shopId);
            }
        } else if (fromItem instanceof SubJoin) {
            SubJoin subJoin = (SubJoin) fromItem;
            processFromItem(subJoin.getLeft(), shopId);
            for (Join join : subJoin.getJoinList()) {
                processFromItem(join.getRightItem(), shopId);
            }
        } else if (fromItem instanceof LateralSubSelect) {
            LateralSubSelect lateralSubSelect = (LateralSubSelect) fromItem;
            if (lateralSubSelect.getSubSelect() != null) {
                processSelectBody(lateralSubSelect.getSubSelect().getSelectBody(), shopId);
            }
        } else if (fromItem instanceof ValuesList) {
            // 处理 VALUES 子句作为数据源的情况
            // 不需要添加租户条件
        }
    }

    protected void handleInsert(Insert insert, String shopId) {
        if (isIgnoreTable(insert.getTable().getName())) {
            return;
        }

        // 添加租户ID字段
        List<Column> columns = insert.getColumns();
        if (!columns.contains(new Column(TENANT_COLUMN))) {
            columns.add(new Column(TENANT_COLUMN));
        }

        // 处理VALUES语句
        ItemsList itemsList = insert.getItemsList();
        if (itemsList instanceof ExpressionList) {

            ExpressionList expressionList = (ExpressionList) itemsList;
            boolean isRow = false;
            List<Expression> expressions = expressionList.getExpressions();
            for (Expression expression : expressions) {
                if (expression instanceof RowConstructor) {
                    //多行插入
                    isRow = true;
                    ((RowConstructor) expression).getExprList().addExpressions(new StringValue(shopId));
                }
            }
            if (!isRow) {
                // 单行插入
                expressionList.getExpressions().add(new StringValue(shopId));
            }
        } else if (itemsList instanceof MultiExpressionList) {
            // 多行插入
            MultiExpressionList multiExprList = (MultiExpressionList) itemsList;
            List<ExpressionList> oldList = multiExprList.getExprList();

            // 创建新的表达式列表
            List<ExpressionList> newList = new ArrayList<>();

            // 处理每一行
            for (ExpressionList oldExpr : oldList) {
                ExpressionList newExpr = new ExpressionList();
                List<Expression> expressions = new ArrayList<>();
                for (Expression expr : oldExpr.getExpressions()) {
                    expressions.add(expr);
                }
                expressions.add(new StringValue(shopId));
                newExpr.setExpressions(expressions);
                newList.add(newExpr);
            }

            // 替换原有的表达式列表
            oldList.clear();
            oldList.addAll(newList);
        } else if (insert.getSelect() != null) {
            Select select = insert.getSelect();
            SelectBody selectBody = select.getSelectBody();

            if (selectBody instanceof PlainSelect) {
                PlainSelect plainSelect = (PlainSelect) selectBody;

                // 在SELECT语句中添加租户ID常量
                SelectExpressionItem tenantItem = new SelectExpressionItem();
                tenantItem.setExpression(new StringValue(shopId));
                plainSelect.getSelectItems().add(tenantItem);

                // 处理SELECT中的租户过滤
                processPlainSelect(plainSelect, shopId);
            } else {
                processSelectBody(selectBody, shopId);
            }
        }
    }

    protected void handleUpdate(Update update, String shopId) {
        if (!isIgnoreTable(update.getTable().getName())) {
            // 处理子查询
            if (update.getWhere() != null) {
                processWhereSubSelect(update.getWhere(), shopId);
            }
            // 处理SET子句中的子查询和CASE表达式
            if (update.getExpressions() != null) {
                for (Expression expression : update.getExpressions()) {
                    if (expression instanceof CaseExpression) {
                        CaseExpression caseExpr = (CaseExpression) expression;
                        // 处理 WHEN 子句
                        if (caseExpr.getWhenClauses() != null) {
                            for (WhenClause whenClause : caseExpr.getWhenClauses()) {
                                processWhereSubSelect(whenClause.getWhenExpression(), shopId);
                                if (whenClause.getThenExpression() != null) {
                                    processWhereSubSelect(whenClause.getThenExpression(), shopId);
                                }
                            }
                        }
                        // 处理 ELSE 子句
                        if (caseExpr.getElseExpression() != null) {
                            processWhereSubSelect(caseExpr.getElseExpression(), shopId);
                        }
                    } else {
                        processWhereSubSelect(expression, shopId);
                    }
                }
            }
            // 添加租户条件
            addTenantCondition(update, shopId);
        }
    }

    protected void handleDelete(Delete delete, String shopId) {
        if (!isIgnoreTable(delete.getTable().getName())) {
            // 处理子查询
            if (delete.getWhere() != null) {
                processWhereSubSelect(delete.getWhere(), shopId);
            }
            // 添加租户条件
            addTenantCondition(delete, shopId);
        }
    }

    protected void processWhereSubSelect(Expression where, String shopId) {
        // 如果shopId为空，不进行处理
        if (StringUtils.isBlank(shopId)) {
            return;
        }

        if (where instanceof SubSelect) {
            SubSelect subSelect = (SubSelect) where;
            if (subSelect.getSelectBody() != null) {
                processSelectBody(subSelect.getSelectBody(), shopId);
            }
        } else if (where instanceof InExpression) {
            InExpression inExpression = (InExpression) where;
            if (inExpression.getRightExpression() instanceof SubSelect) {
                SubSelect subSelect = (SubSelect) inExpression.getRightExpression();
                processSelectBody(subSelect.getSelectBody(), shopId);
            }
        } else if (where instanceof ExistsExpression) {
            ExistsExpression existsExpression = (ExistsExpression) where;
            if (existsExpression.getRightExpression() instanceof SubSelect) {
                processSelectBody(((SubSelect) existsExpression.getRightExpression()).getSelectBody(), shopId);
            }
        } else if (where instanceof CaseExpression) {
            CaseExpression caseExpression = (CaseExpression) where;
            // 处理 WHEN 子句
            if (caseExpression.getWhenClauses() != null) {
                for (WhenClause whenClause : caseExpression.getWhenClauses()) {
                    processWhereSubSelect(whenClause.getWhenExpression(), shopId);
                }
            }
            // 处理 ELSE 子句
            if (caseExpression.getElseExpression() != null) {
                processWhereSubSelect(caseExpression.getElseExpression(), shopId);
            }
        } else if (where instanceof EqualsTo) {
            EqualsTo equalsTo = (EqualsTo) where;

            // 处理左表达式中的子查询
            if (equalsTo.getLeftExpression() instanceof SubSelect) {
                SubSelect subSelect = (SubSelect) equalsTo.getLeftExpression();
                processSelectBody(subSelect.getSelectBody(), shopId);
            }

            // 处理右表达式中的子查询
            if (equalsTo.getRightExpression() instanceof SubSelect) {
                SubSelect subSelect = (SubSelect) equalsTo.getRightExpression();
                processSelectBody(subSelect.getSelectBody(), shopId);
            } else if (equalsTo.getRightExpression() instanceof Function) {
                Function function = (Function) equalsTo.getRightExpression();

                // 处理函数参数中的子查询
                if (function.getParameters() != null) {
                    for (Expression expr : function.getParameters().getExpressions()) {
                        if (expr instanceof SubSelect) {
                            SubSelect subSelect = (SubSelect) expr;
                            processSelectBody(subSelect.getSelectBody(), shopId);
                        }
                    }
                }
            }
        } else if (where instanceof AndExpression) {
            AndExpression andExpression = (AndExpression) where;
            processWhereSubSelect(andExpression.getLeftExpression(), shopId);
            processWhereSubSelect(andExpression.getRightExpression(), shopId);
        } else if (where instanceof OrExpression) {
            OrExpression orExpression = (OrExpression) where;
            processWhereSubSelect(orExpression.getLeftExpression(), shopId);
            processWhereSubSelect(orExpression.getRightExpression(), shopId);
        }
    }

    protected void addTenantCondition(Update update, String shopId) {
        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(new Column(TENANT_COLUMN));
        equalsTo.setRightExpression(new StringValue(shopId));

        Expression where = update.getWhere();
        if (where != null) {
            update.setWhere(new AndExpression(where, equalsTo));
        } else {
            update.setWhere(equalsTo);
        }
    }

    protected void addTenantCondition(Delete delete, String shopId) {
        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(new Column(TENANT_COLUMN));
        equalsTo.setRightExpression(new StringValue(shopId));

        Expression where = delete.getWhere();
        if (where != null) {
            delete.setWhere(new AndExpression(where, equalsTo));
        } else {
            delete.setWhere(equalsTo);
        }
    }

    protected boolean needProcess(String sql) {
        if (StringUtils.isBlank(sql)) {
            return false;
        }
        sql = sql.toLowerCase();
        // 判断是否为需要处理的SQL类型
        if (!sql.startsWith("select") && !sql.startsWith("insert") &&
                !sql.startsWith("update") && !sql.startsWith("delete")) {
            return false;
        }
        // 判断是否包含忽略的表
        ISysTenantIgnoreTableService service = getTenantIgnoreTableService();
        if (service != null) {
            Set<String> ignoreTableNames = service.getActiveIgnoreTableNames();
            for (String ignoreTable : ignoreTableNames) {
                if (sql.contains(ignoreTable.toLowerCase())) {
                    return false;
                }
            }
        } else {
            // 如果Service还未初始化，使用默认的忽略表列表
            for (String ignoreTable : DEFAULT_IGNORE_TABLES) {
                if (sql.contains(ignoreTable.toLowerCase())) {
                    return false;
                }
            }
        }
        return true;
    }

    protected boolean isIgnoreTable(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return false;
        }
        ISysTenantIgnoreTableService service = getTenantIgnoreTableService();
        if (service != null) {
            Set<String> ignoreTableNames = service.getActiveIgnoreTableNames();
            for (String ignoreTable : ignoreTableNames) {
                if (ignoreTable.equalsIgnoreCase(tableName)) {
                    return true;
                }
            }
        } else {
            // 如果Service还未初始化，使用默认的忽略表列表
            for (String ignoreTable : DEFAULT_IGNORE_TABLES) {
                if (ignoreTable.equalsIgnoreCase(tableName)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }
} 